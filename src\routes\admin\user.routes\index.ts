import express from "express";
import { storageData } from "../../../utils/services/multer";
import {
  destroyUserById,
  getAllDoctors,
  getAllUsers,
  getUsersById,
  updateUser,
  getAllPatientsWithDoctors,
  getPatientById,
  getAllSpecialists,
  getAllRoles,
  cancelPatientVersion,
  getDeletedUsers,
  restoreUserById,
} from "../../../controller/admin/user.controller";

const router = express.Router();
const upload = storageData("users");

// Routes
router.get("/", getAllUsers);
router.get("/deleted", getDeletedUsers); // Get all deleted users
router.patch(
  "/:id",
  upload.fields([{ name: "profile_image", maxCount: 1 }]),
  updateUser
);
router.delete("/:id", destroyUserById);
router.put("/:id/restore", restoreUserById); // Restore deleted user
router.get("/doctors", getAllDoctors);
router.get("/specialists", getAllSpecialists);
router.get("/roles", getAllRoles);
router.get("/patients", getAllPatientsWithDoctors);
router.patch("/patients/:id/cancel", cancelPatientVersion);
router.get("/patients/:id", getPatientById);

router.get("/:id", getUsersById);

export default router;
